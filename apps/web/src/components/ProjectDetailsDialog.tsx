import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Project, UserRole, isManager, getBaseRole, SUPERVISOR_STATUS_LABELS, SupervisorStatus, User } from "@/types/project";
import { Calendar, Save, Edit2, X, Trash2, User as UserIcon } from "lucide-react";
import { toast } from "sonner";
import { formatDate, formatDateTime, formatCurrency } from "@/lib/datetime";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SupervisorAssignInline } from "@/components/SupervisorAssignInline";

interface ProjectDetailsDialogProps {
  project: Project | null;
  isOpen: boolean;
  onClose: () => void;
  currentUserRole: UserRole;
  currentUserId: string;
  onUpdateExpiredDate?: (projectId: string, newExpiredDate: string) => void;
  onAssignTask?: (projectId: string, assigneeId: string, phases?: string[]) => void;
  onDeleteProject?: (projectId: string) => void;
  users?: User[];
}

export const ProjectDetailsDialog = ({
  project,
  isOpen,
  onClose,
  currentUserRole,
  currentUserId,
  onUpdateExpiredDate,
  onAssignTask,
  onDeleteProject,
  users = []
}: ProjectDetailsDialogProps) => {
  const [isEditingExpiredDate, setIsEditingExpiredDate] = useState(false);
  const [newExpiredDate, setNewExpiredDate] = useState('');

  if (!project) return null;

  const baseRole = getBaseRole(currentUserRole);
  const canEditExpiredDate = isManager(currentUserRole);

  const handleEditExpiredDate = () => {
    setNewExpiredDate(project.expiredDate || '');
    setIsEditingExpiredDate(true);
  };

  const handleSaveExpiredDate = () => {
    if (newExpiredDate && onUpdateExpiredDate) {
      onUpdateExpiredDate(project.id, newExpiredDate);
      setIsEditingExpiredDate(false);
      toast.success("Expired date updated successfully");
    }
  };

  const handleCancelEdit = () => {
    setIsEditingExpiredDate(false);
    setNewExpiredDate('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{project.title}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Client</label>
              <p>{project.client}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Status</label>
              <Badge className="ml-2">{project.status}</Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Created</label>
              <p>{formatDate(project.createdAt)}</p>
            </div>
            {project.salesAmount && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Sales Amount</label>
                <p>{formatCurrency(project.salesAmount)}</p>
              </div>
            )}
            {project.dueDate && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Due Date</label>
                <p>{formatDate(project.dueDate)}</p>
              </div>
            )}
            {project.expiredDate && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Expired Date</label>
                <div className="flex items-center gap-2">
                  {isEditingExpiredDate ? (
                    <div className="flex items-center gap-2">
                      <Input
                        type="date"
                        value={newExpiredDate}
                        onChange={(e) => setNewExpiredDate(e.target.value)}
                        className="w-auto"
                      />
                      <Button size="sm" onClick={handleSaveExpiredDate}>
                        <Save className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <p>{formatDate(project.expiredDate)}</p>
                      {canEditExpiredDate && (
                        <Button size="sm" variant="ghost" onClick={handleEditExpiredDate}>
                          <Edit2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Manager actions moved here: assign or delete, filtered by task role */}
          {isManager(currentUserRole) && (
            <div className="border rounded p-3 bg-muted/30">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-muted-foreground">Manager Actions</span>
              </div>
              <div className="flex gap-2 flex-wrap">
                {/* Delete */}
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4 mr-1" /> Delete (Lost Deal)
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
                      <AlertDialogDescription>
                        Move this task to Lost Deal? It will be removed from the board but remain in Case History.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={() => onDeleteProject && onDeleteProject(project.id)}>Confirm</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                {/* Assign/Reassign filtered by task role */}
                {/* Assign/Reassign filtered by task role; for supervisors include phases */}
                {(['supervisor_pending_assign','inprogress','completed','floor_protection','plaster_ceiling','spc','first_painting','carpentry_measure','measure_others','carpentry_install','quartz_measure','quartz_install','glass_measure','glass_install','final_wiring','final_painting','install_others','plumbing','cleaning','defects'] as string[]).includes(project.status) ? (
                  <SupervisorAssignInline users={users} projectId={project.id} onAssign={onAssignTask} />
                ) : (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="default" size="sm">
                        <UserIcon className="h-4 w-4 mr-1" /> Assign / Reassign
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Assign Task</AlertDialogTitle>
                        <AlertDialogDescription>Select a user to assign this task to:</AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="py-2">
                        <Select onValueChange={(value) => onAssignTask && onAssignTask(project.id, value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select assignee" />
                          </SelectTrigger>
                          <SelectContent>
                            {users
                              .filter((u) => {
                                const s = project.status as string;
                                const isDesigner = ['designer_pending_assign','checklist','3d','2d','furniture_list','complete'].includes(s);
                                if (isDesigner) return u.role === 'designer';
                                return u.role === 'sales';
                              })
                              .map((usr) => (
                                <SelectItem key={usr.id} value={usr.id}>
                                  {usr.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </div>
            </div>
          )}

          <div>
            <label className="text-sm font-medium text-muted-foreground">Project Type</label>
            <p className="mt-1">Project workflow task</p>
          </div>

          {project.completionProof && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Completion Notes</label>
              <p className="mt-1">{project.completionProof}</p>
            </div>
          )}
          
          {(project.revisions3d?.length || project.revisions2d?.length) && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Revision History</label>
              <div className="mt-2 space-y-3">
                {project.revisions3d && project.revisions3d.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-blue-600 mb-2">3D Design Revisions</h4>
                    <div className="space-y-2">
                      {project.revisions3d.map((revision, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center gap-2 text-sm">
                            <Badge variant="outline" className="text-xs bg-blue-50">
                              3D Rev {index + 1}
                            </Badge>
                            <span className="text-muted-foreground">
                              {formatDateTime(revision)}
                            </span>
                          </div>
                          {project.expiredDate && (
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              <span>Expired: {formatDate(project.expiredDate)}</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    {project.revisions3d.length > 3 && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-700 font-medium">
                          ⚠️ High 3D revision count ({project.revisions3d.length}) - Manager attention required
                        </p>
                      </div>
                    )}
                  </div>
                )}
                {project.revisions2d && project.revisions2d.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-green-600 mb-2">2D Design Revisions</h4>
                    <div className="space-y-2">
                      {project.revisions2d.map((revision, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center gap-2 text-sm">
                            <Badge variant="outline" className="text-xs bg-green-50">
                              2D Rev {index + 1}
                            </Badge>
                            <span className="text-muted-foreground">
                              {formatDateTime(revision)}
                            </span>
                          </div>
                          {project.expiredDate && (
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              <span>Expired: {formatDate(project.expiredDate)}</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    {project.revisions2d.length > 3 && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-700 font-medium">
                          ⚠️ High 2D revision count ({project.revisions2d.length}) - Manager attention required
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Supervisor Phase Completion History */}
          {project.supervisorPhaseDates && Object.keys(project.supervisorPhaseDates).length > 0 && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Supervisor Phase Completion History</label>
              <div className="mt-2 space-y-2">
                {Object.entries(project.supervisorPhaseDates as Record<string, string>)
                  .sort(([, dateA], [, dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                  .map(([phase, completionDate]) => (
                    <div key={phase} className="flex items-center justify-between p-3 border rounded-lg bg-gradient-to-r from-orange-50 to-green-50">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="text-xs bg-orange-100 text-orange-700 border-orange-200">
                          ✓ Completed
                        </Badge>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {SUPERVISOR_STATUS_LABELS[phase as SupervisorStatus] || phase}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Phase: {phase}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">
                          {formatDateTime(completionDate)}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">
                  📋 Total completed phases: {Object.keys(project.supervisorPhaseDates).length}
                  {project.supervisorSelectedPhases && project.supervisorSelectedPhases.length > 0 && (
                    <span className="ml-2">
                      | Selected phases: {project.supervisorSelectedPhases.length}
                    </span>
                  )}
                </p>
              </div>
            </div>
          )}

          {/* Supervisor Selected Phases */}
          {project.supervisorSelectedPhases && project.supervisorSelectedPhases.length > 0 && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Assigned Supervisor Phases</label>
              <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                {project.supervisorSelectedPhases.map((phase) => {
                  const isCompleted = project.supervisorPhaseDates && project.supervisorPhaseDates[phase];
                  return (
                    <div
                      key={phase}
                      className={`p-2 border rounded-lg ${
                        isCompleted
                          ? 'bg-green-50 border-green-200'
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            isCompleted
                              ? 'bg-green-100 text-green-700 border-green-300'
                              : 'bg-gray-100 text-gray-600 border-gray-300'
                          }`}
                        >
                          {isCompleted ? '✓' : '○'}
                        </Badge>
                        <span className="text-sm font-medium">
                          {SUPERVISOR_STATUS_LABELS[phase as SupervisorStatus] || phase}
                        </span>
                      </div>
                      {isCompleted && (
                        <p className="text-xs text-green-600 mt-1 ml-6">
                          Completed: {formatDate(project.supervisorPhaseDates[phase])}
                        </p>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};