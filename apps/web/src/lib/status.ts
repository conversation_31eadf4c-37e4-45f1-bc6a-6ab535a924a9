import { PROJECT_STATUS_LABELS, type ProjectStatus, type SalesStatus, type DesignerStatus } from "@/types/project";

export type StatusTone = "pending" | "in-progress" | "review" | "complete" | "destructive" | "muted";

export const toneToClasses = (tone: StatusTone): string => {
  switch (tone) {
    case "pending":
      return "bg-status-pending text-status-pending-foreground";
    case "in-progress":
      return "bg-status-in-progress text-status-in-progress-foreground";
    case "review":
      return "bg-status-review text-status-review-foreground";
    case "complete":
      return "bg-status-complete text-status-complete-foreground";
    case "destructive":
      return "bg-destructive text-destructive-foreground";
    default:
      return "bg-muted text-muted-foreground";
  }
};

const SALES_IN_PROGRESS: SalesStatus[] = ["lead", "quotation", "potential"];
const DESIGNER_IN_PROGRESS: DesignerStatus[] = ["checklist", "3d", "2d", "furniture_list"];

export const statusToTone = (status: ProjectStatus | string): StatusTone => {
  // Sales
  if ((["won_deal", "completed"] as string[]).includes(status)) return "complete";
  if (status === "lost_deal") return "destructive";
  if ((SALES_IN_PROGRESS as string[]).includes(status)) return "in-progress";

  // Designer
  if (status === "designer_pending_assign") return "pending";
  if (status === "designer_lost_deal") return "destructive";
  if ((DESIGNER_IN_PROGRESS as string[]).includes(status)) return "in-progress";
  if (status === "complete") return "complete";

  // Supervisor
  if (status === "supervisor_pending_assign") return "pending";
  if (status === "supervisor_lost_deal") return "destructive";
  if (status === "inprogress") return "in-progress";
  if (status === "completed") return "complete";

  // Legacy/general
  if (status === "deposit") return "pending";
  if (["design", "procurement", "installation"].includes(status)) return "in-progress";
  if (status === "complete") return "complete";

  // Any other phases and legacy sub-statuses fall back to in-progress for consistency
  return "in-progress";
};

export const statusLabel = (status: ProjectStatus | string): string => {
  return PROJECT_STATUS_LABELS[status] || (status as string);
};

